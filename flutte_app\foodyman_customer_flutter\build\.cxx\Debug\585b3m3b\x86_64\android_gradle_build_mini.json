{"buildFiles": ["C:\\Users\\<USER>\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\OSPanel\\home\\flutte_app\\foodyman_customer_flutter\\build\\.cxx\\Debug\\585b3m3b\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\OSPanel\\home\\flutte_app\\foodyman_customer_flutter\\build\\.cxx\\Debug\\585b3m3b\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}