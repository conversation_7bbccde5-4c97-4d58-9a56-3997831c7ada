import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:pandoo_delivery/infrastructure/services/payment_debug_service.dart';
import 'package:pandoo_delivery/infrastructure/services/payment_fallback_service.dart';
import 'package:pandoo_delivery/infrastructure/models/data/order_body_data.dart';
import 'package:pandoo_delivery/infrastructure/models/data/location_data.dart';
import 'package:pandoo_delivery/infrastructure/services/enums.dart';
import 'package:pandoo_delivery/presentation/theme/theme.dart';

class PaymentDebugPage extends ConsumerStatefulWidget {
  const PaymentDebugPage({Key? key}) : super(key: key);

  @override
  ConsumerState<PaymentDebugPage> createState() => _PaymentDebugPageState();
}

class _PaymentDebugPageState extends ConsumerState<PaymentDebugPage> {
  final List<String> _testPaymentTags = [
    'cash',
    'cash_delivery',
    'card',
    'pix',
    'wallet',
    'stripe',
    'invalid_tag',
  ];

  String _selectedTag = 'cash';
  String _debugOutput = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment Debug'),
        backgroundColor: AppStyle.primary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Payment Tag Selector
            const Text(
              'Select Payment Tag:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            DropdownButton<String>(
              value: _selectedTag,
              isExpanded: true,
              items: _testPaymentTags.map((tag) {
                return DropdownMenuItem(
                  value: tag,
                  child: Text(tag),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedTag = value ?? 'cash';
                });
              },
            ),
            
            const SizedBox(height: 20),
            
            // Test Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _testPaymentValidation,
                    child: const Text('Test Validation'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _testPaymentFlow,
                    child: const Text('Test Full Flow'),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 8),
            
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _testFallback,
                    child: const Text('Test Fallback'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _clearOutput,
                    child: const Text('Clear'),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 20),
            
            // Debug Output
            const Text(
              'Debug Output:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.grey[100],
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _debugOutput.isEmpty ? 'No debug output yet...' : _debugOutput,
                    style: const TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 12,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _testPaymentValidation() {
    setState(() {
      _debugOutput += '\n=== PAYMENT VALIDATION TEST ===\n';
      _debugOutput += 'Original Tag: $_selectedTag\n';
      
      final correctedTag = PaymentFallbackService.getCorrectPaymentTag(_selectedTag);
      _debugOutput += 'Corrected Tag: $correctedTag\n';
      
      final isValid = PaymentFallbackService.isValidPaymentTag(correctedTag);
      _debugOutput += 'Is Valid: $isValid\n';
      
      final shouldSkip = PaymentFallbackService.shouldSkipProcessing(correctedTag);
      _debugOutput += 'Should Skip Processing: $shouldSkip\n';
      
      final displayName = PaymentFallbackService.getPaymentDisplayName(correctedTag);
      _debugOutput += 'Display Name: $displayName\n';
      
      _debugOutput += '================================\n';
    });
  }

  void _testPaymentFlow() {
    setState(() {
      _debugOutput += '\n=== PAYMENT FLOW TEST ===\n';
      
      final orderBody = _createTestOrderBody();
      
      PaymentDebugService.debugPaymentFlow(
        paymentTag: _selectedTag,
        orderBody: orderBody,
      );
      
      _debugOutput += 'Payment flow debug completed (check console for detailed logs)\n';
      _debugOutput += '==========================\n';
    });
  }

  void _testFallback() {
    setState(() {
      _debugOutput += '\n=== FALLBACK TEST ===\n';
      
      final endpoints = PaymentFallbackService.getFallbackEndpoints(_selectedTag);
      _debugOutput += 'Fallback Endpoints:\n';
      for (int i = 0; i < endpoints.length; i++) {
        _debugOutput += '${i + 1}. ${endpoints[i]}\n';
      }
      
      _debugOutput += '===================\n';
    });
  }

  void _clearOutput() {
    setState(() {
      _debugOutput = '';
    });
  }

  OrderBodyData _createTestOrderBody() {
    return OrderBodyData(
      cartId: 24,
      shopId: 501,
      paymentId: 1,
      deliveryFee: 5.0,
      deliveryType: DeliveryTypeEnum.delivery,
      location: const Location(latitude: -18.43775258003136, longitude: -50.43386545032263),
      address: AddressModel(
        address: 'Test Address, 123',
        house: '123',
        floor: '1',
        office: 'Apt 1',
      ),
      deliveryDate: '2025-08-03',
      deliveryTime: '12:00',
      notes: [],
      note: 'Test order for payment debugging',
    );
  }
}
