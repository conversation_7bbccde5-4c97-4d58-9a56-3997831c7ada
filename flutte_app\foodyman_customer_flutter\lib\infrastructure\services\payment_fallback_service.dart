import 'package:flutter/foundation.dart';

class PaymentFallbackService {
  /// Map of payment tags that might need fallback
  static const Map<String, String> _paymentTagMap = {
    'cash_delivery': 'cash',
    'card_delivery': 'card',
    'pix_delivery': 'pix',
    'credit_card': 'card',
    'debit_card': 'card',
    'money': 'cash',
    'dinheiro': 'cash',
    'cartao': 'card',
  };
  
  /// Get the correct payment tag for API
  static String getCorrectPaymentTag(String originalTag) {
    final correctedTag = _paymentTagMap[originalTag] ?? originalTag;
    
    if (kDebugMode && correctedTag != originalTag) {
      print('PaymentFallback: Corrected tag "$originalTag" to "$correctedTag"');
    }
    
    return correctedTag;
  }
  
  /// Check if payment tag is valid for API
  static bool isValidPaymentTag(String tag) {
    const validTags = [
      'cash',
      'card',
      'pix',
      'wallet',
      'stripe',
      'paypal',
      'pay-fast',
      'mercado-pago',
      'razorpay',
      'paystack',
      'flutterwave',
    ];
    
    return validTags.contains(tag);
  }
  
  /// Get fallback endpoint if primary fails
  static List<String> getFallbackEndpoints(String paymentTag) {
    final correctedTag = getCorrectPaymentTag(paymentTag);
    
    return [
      '/api/v1/dashboard/user/order-$correctedTag-process',
      '/api/v1/rest/order-$correctedTag-process',
      '/api/v1/dashboard/order-$correctedTag-process',
      '/api/v1/order-$correctedTag-process',
    ];
  }
  
  /// Check if payment should skip processing (direct order creation)
  static bool shouldSkipProcessing(String paymentTag) {
    const skipProcessingTags = ['cash', 'wallet'];
    final correctedTag = getCorrectPaymentTag(paymentTag);
    return skipProcessingTags.contains(correctedTag);
  }
  
  /// Get payment method display name in Portuguese
  static String getPaymentDisplayName(String tag) {
    const displayNames = {
      'cash': 'Dinheiro na Entrega',
      'card': 'Cartão na Entrega',
      'pix': 'PIX na Entrega',
      'wallet': 'Carteira Digital',
      'stripe': 'Cartão de Crédito',
      'paypal': 'PayPal',
      'pay-fast': 'PayFast',
      'mercado-pago': 'Mercado Pago',
    };
    
    return displayNames[tag] ?? tag.toUpperCase();
  }
  
  /// Validate order data for payment processing
  static Map<String, dynamic> validateOrderData(Map<String, dynamic> orderData) {
    final validatedData = Map<String, dynamic>.from(orderData);
    
    // Ensure required fields are present
    if (!validatedData.containsKey('cart_id')) {
      throw Exception('cart_id is required for order processing');
    }
    
    if (!validatedData.containsKey('shop_id')) {
      throw Exception('shop_id is required for order processing');
    }
    
    if (!validatedData.containsKey('payment_id')) {
      throw Exception('payment_id is required for order processing');
    }
    
    // Set default values for missing optional fields
    validatedData['currency_id'] ??= 1;
    validatedData['rate'] ??= 1;
    validatedData['delivery_type'] ??= 'delivery';
    validatedData['type'] ??= 'mobile';
    
    if (kDebugMode) {
      print('PaymentFallback: Order data validated successfully');
    }
    
    return validatedData;
  }
}
