class GoogleMapsConfig {
  // API Keys - SUBSTITUA PELAS SUAS CHAVES REAIS
  static const String androidApiKey = 'SUA_ANDROID_API_KEY_AQUI';
  static const String iosApiKey = 'SUA_IOS_API_KEY_AQUI';
  static const String webApiKey = 'SUA_WEB_API_KEY_AQUI';
  
  // Configurações de Mapa para Delivery
  static const double defaultZoom = 15.0;
  static const double trackingZoom = 18.0;
  static const double cityZoom = 12.0;
  
  // Configurações de Localização
  static const int locationUpdateInterval = 5000; // 5 segundos
  static const int fastestLocationInterval = 2000; // 2 segundos
  static const double smallestDisplacement = 10.0; // 10 metros
  
  // Configurações de Geocoding
  static const String language = 'pt-BR';
  static const String region = 'BR';
  
  // Configurações de Places API
  static const List<String> placeTypes = [
    'restaurant',
    'food',
    'meal_takeaway',
    'establishment'
  ];
  
  // Configurações de Directions API
  static const String travelMode = 'driving';
  static const bool avoidTolls = false;
  static const bool avoidHighways = false;
  
  // Configurações de Distance Matrix
  static const String units = 'metric';
  static const List<String> avoidFeatures = [];
  
  // Limites de Delivery (em metros)
  static const double maxDeliveryDistance = 15000; // 15km
  static const double minDeliveryDistance = 500;   // 500m
  
  // Configurações de Rastreamento
  static const int trackingUpdateInterval = 3000; // 3 segundos
  static const double trackingAccuracy = 10.0;    // 10 metros
}
