<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="ea2adbb6-4b0c-46c8-abd8-3739c2100f84" name="Alterações" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="30OaaoiQbZ1mCJfNpltNDnkLagT" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Flutter.main.dart.executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.cidr.known.project.marker": "true",
    "RunOnceActivity.readMode.enableVisualFormatting": "true",
    "ScreenshotViewer.SavePath": "C:/Users/<USER>/Downloads",
    "cf.first.check.clang-format": "false",
    "cidr.known.project.marker": "true",
    "com.google.services.firebase.aqiPopupShown": "true",
    "dart.analysis.tool.window.visible": "false",
    "ignore.virus.scanning.warn.message": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "C:/OSPanel/home/<USER>/foodyman_customer_flutter/android/app",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "show.migrate.to.gradle.popup": "false"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\OSPanel\home\flutte_app\foodyman_customer_flutter\android\app" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="main.dart" type="FlutterRunConfigurationType" factoryName="Flutter">
      <option name="filePath" value="$PROJECT_DIR$/lib/main.dart" />
      <method v="2" />
    </configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Tarefa padrão">
      <changelist id="ea2adbb6-4b0c-46c8-abd8-3739c2100f84" name="Alterações" comment="" />
      <created>1753501138748</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753501138748</updated>
    </task>
    <servers />
  </component>
  <component name="UnknownFeatures">
    <option featureType="com.intellij.fileTypeFactory" implementationName="*.storyboard" />
  </component>
</project>