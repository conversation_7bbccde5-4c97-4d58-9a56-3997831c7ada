import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:pandoo_delivery/infrastructure/models/data/order_body_data.dart';

class PaymentDebugService {
  static const String _tag = 'PaymentDebug';
  
  /// Log payment method details
  static void logPaymentMethod(String tag, Map<String, dynamic>? paymentData) {
    if (kDebugMode) {
      print('$_tag: Payment Method Selected');
      print('$_tag: Tag: $tag');
      print('$_tag: Payment Data: ${jsonEncode(paymentData)}');
    }
  }
  
  /// Log order body data before sending to API
  static void logOrderBody(OrderBodyData orderBody, String paymentTag) {
    if (kDebugMode) {
      print('$_tag: Order Body Data');
      print('$_tag: Payment Tag: $paymentTag');
      print('$_tag: Cart ID: ${orderBody.cartId}');
      print('$_tag: Shop ID: ${orderBody.shopId}');
      print('$_tag: Payment ID: ${orderBody.paymentId}');
      print('$_tag: Delivery Type: ${orderBody.deliveryType}');
      print('$_tag: Full JSON: ${jsonEncode(orderBody.toJson())}');
    }
  }
  
  /// Log API endpoint being called
  static void logApiEndpoint(String method, String endpoint, Map<String, dynamic>? data) {
    if (kDebugMode) {
      print('$_tag: API Call');
      print('$_tag: Method: $method');
      print('$_tag: Endpoint: $endpoint');
      print('$_tag: Data: ${jsonEncode(data)}');
    }
  }
  
  /// Log API response
  static void logApiResponse(int statusCode, dynamic response) {
    if (kDebugMode) {
      print('$_tag: API Response');
      print('$_tag: Status Code: $statusCode');
      print('$_tag: Response: ${jsonEncode(response)}');
    }
  }
  
  /// Log API error
  static void logApiError(String error, String? endpoint) {
    if (kDebugMode) {
      print('$_tag: API Error');
      print('$_tag: Endpoint: $endpoint');
      print('$_tag: Error: $error');
    }
  }
  
  /// Validate payment tag
  static bool validatePaymentTag(String tag) {
    const validTags = [
      'cash',
      'card',
      'pix',
      'wallet',
      'stripe',
      'paypal',
      'pay-fast',
      'mercado-pago',
    ];
    
    final isValid = validTags.contains(tag);
    if (kDebugMode) {
      print('$_tag: Payment Tag Validation');
      print('$_tag: Tag: $tag');
      print('$_tag: Valid: $isValid');
      if (!isValid) {
        print('$_tag: Valid tags: $validTags');
      }
    }
    
    return isValid;
  }
  
  /// Check if payment requires processing
  static bool requiresProcessing(String tag) {
    const noProcessingTags = ['cash', 'wallet'];
    final requires = !noProcessingTags.contains(tag);
    
    if (kDebugMode) {
      print('$_tag: Payment Processing Check');
      print('$_tag: Tag: $tag');
      print('$_tag: Requires Processing: $requires');
    }
    
    return requires;
  }
  
  /// Generate expected endpoint for payment
  static String getExpectedEndpoint(String tag) {
    final endpoint = '/api/v1/dashboard/user/order-$tag-process';
    
    if (kDebugMode) {
      print('$_tag: Expected Endpoint');
      print('$_tag: Tag: $tag');
      print('$_tag: Endpoint: $endpoint');
    }
    
    return endpoint;
  }
  
  /// Comprehensive payment debug
  static void debugPaymentFlow({
    required String paymentTag,
    required OrderBodyData orderBody,
    Map<String, dynamic>? paymentData,
  }) {
    if (kDebugMode) {
      print('\n$_tag: ========== PAYMENT FLOW DEBUG ==========');
      logPaymentMethod(paymentTag, paymentData);
      validatePaymentTag(paymentTag);
      logOrderBody(orderBody, paymentTag);
      
      if (requiresProcessing(paymentTag)) {
        getExpectedEndpoint(paymentTag);
      } else {
        print('$_tag: Payment will use createOrder directly (no processing)');
      }
      print('$_tag: ========================================\n');
    }
  }
}
