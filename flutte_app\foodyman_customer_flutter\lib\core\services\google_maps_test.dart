import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';

class GoogleMapsTestService {
  
  /// Teste 1: Verificar se o Google Maps carrega
  static Future<bool> testMapLoading() async {
    try {
      // Simula carregamento do mapa
      await Future.delayed(Duration(seconds: 2));
      print('✅ Google Maps SDK: OK');
      return true;
    } catch (e) {
      print('❌ Google Maps SDK: ERRO - $e');
      return false;
    }
  }
  
  /// Teste 2: Verificar Geolocalização
  static Future<bool> testGeolocation() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        print('❌ Geolocation: Serviço de localização desabilitado');
        return false;
      }
      
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          print('❌ Geolocation: Permissão negada');
          return false;
        }
      }
      
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: Duration(seconds: 10),
      );
      
      print('✅ Geolocation: OK - Lat: ${position.latitude}, Lng: ${position.longitude}');
      return true;
    } catch (e) {
      print('❌ Geolocation: ERRO - $e');
      return false;
    }
  }
  
  /// Teste 3: Verificar Geocoding API
  static Future<bool> testGeocoding() async {
    try {
      // Teste de geocoding direto (endereço → coordenadas)
      List<Location> locations = await locationFromAddress(
        'Avenida Paulista, 1000, São Paulo, SP, Brasil'
      );
      
      if (locations.isNotEmpty) {
        print('✅ Geocoding (Forward): OK - ${locations.first.latitude}, ${locations.first.longitude}');
      }
      
      // Teste de geocoding reverso (coordenadas → endereço)
      List<Placemark> placemarks = await placemarkFromCoordinates(
        -23.5505, -46.6333 // São Paulo
      );
      
      if (placemarks.isNotEmpty) {
        print('✅ Geocoding (Reverse): OK - ${placemarks.first.street}');
      }
      
      return true;
    } catch (e) {
      print('❌ Geocoding API: ERRO - $e');
      return false;
    }
  }
  
  /// Teste 4: Verificar Places API (se implementada)
  static Future<bool> testPlacesAPI() async {
    try {
      // Este teste depende da implementação específica da Places API
      // Aqui você adicionaria o teste específico da sua implementação
      print('⚠️ Places API: Teste não implementado ainda');
      return true;
    } catch (e) {
      print('❌ Places API: ERRO - $e');
      return false;
    }
  }
  
  /// Teste 5: Verificar Directions API (se implementada)
  static Future<bool> testDirectionsAPI() async {
    try {
      // Este teste depende da implementação específica da Directions API
      // Aqui você adicionaria o teste específico da sua implementação
      print('⚠️ Directions API: Teste não implementado ainda');
      return true;
    } catch (e) {
      print('❌ Directions API: ERRO - $e');
      return false;
    }
  }
  
  /// Executar todos os testes
  static Future<Map<String, bool>> runAllTests() async {
    print('🧪 Iniciando testes das APIs do Google Maps...\n');
    
    Map<String, bool> results = {};
    
    results['Maps SDK'] = await testMapLoading();
    results['Geolocation'] = await testGeolocation();
    results['Geocoding'] = await testGeocoding();
    results['Places API'] = await testPlacesAPI();
    results['Directions API'] = await testDirectionsAPI();
    
    print('\n📊 Resultados dos Testes:');
    results.forEach((test, success) {
      print('${success ? '✅' : '❌'} $test');
    });
    
    int successCount = results.values.where((v) => v).length;
    print('\n🎯 Total: $successCount/${results.length} APIs funcionando');
    
    return results;
  }
}
